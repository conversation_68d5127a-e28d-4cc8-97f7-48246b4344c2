package main

import (
	"io/ioutil"
	"net/http"
	"os"
	"time"

	log "github.com/sirupsen/logrus"
	"github.com/urfave/cli"

	"git.gig.tech/gig-meneja/iam/communication"
	"git.gig.tech/gig-meneja/iam/config"
	"git.gig.tech/gig-meneja/iam/db"
	dborganization "git.gig.tech/gig-meneja/iam/db/organization"
	"git.gig.tech/gig-meneja/iam/globalconfig"
	"git.gig.tech/gig-meneja/iam/identityservice"
	"git.gig.tech/gig-meneja/iam/identityservice/invitations"
	"git.gig.tech/gig-meneja/iam/identityservice/organization"
	"git.gig.tech/gig-meneja/iam/identityservice/security"
	"git.gig.tech/gig-meneja/iam/oauthservice"
	"git.gig.tech/gig-meneja/iam/routes"
	"git.gig.tech/gig-meneja/iam/siteservice"
	"github.com/dgrijalva/jwt-go"
)

var version string

func main() {
	if version == "" {
		version = "Dev"
	}
	app := cli.NewApp()
	app.Name = "Identity server"
	app.Version = version

	log.SetFormatter(&log.TextFormatter{FullTimestamp: true})
	// Set log output to stdout so we can pipe it
	log.SetOutput(os.Stdout)

	var debugLogging, ignoreDevcert, testEnv bool
	var bindAddress, dbConnectionString string
	var twilioAccountSID, twilioAuthToken, twilioMessagingServiceSID string
	var smtpserver, smtpuser, smtppassword, supportEmail string
	var smtpport int

	var smsAeroUser, smsAeroPassword, smsAeroSenderId, rootOrg, ownerEmail, iamHostName, apiKey, callbackURL string

	app.Flags = []cli.Flag{
		cli.BoolFlag{
			Name:        "debug, d",
			Usage:       "Enable debug logging",
			Destination: &debugLogging,
		},
		cli.StringFlag{
			Name:        "bind, b",
			Usage:       "Bind address",
			Value:       ":8080",
			Destination: &bindAddress,
		},
		cli.StringFlag{
			Name:        "connectionstring, c",
			Usage:       "Mongodb connection string",
			Value:       "127.0.0.1:27017",
			Destination: &dbConnectionString,
		},
		cli.BoolFlag{
			Name:        "ignore-devcert, i",
			Usage:       "Ignore default devcert even if exists",
			Destination: &ignoreDevcert,
		},
		cli.StringFlag{
			Name:        "twilio-AccountSID",
			Usage:       "Twilio AccountSID",
			Destination: &twilioAccountSID,
		},
		cli.StringFlag{
			Name:        "twilio-AuthToken",
			Usage:       "Twilio AuthToken",
			Destination: &twilioAuthToken,
		},
		cli.StringFlag{
			Name:        "twilio-MsgSvcSID",
			Usage:       "Twilio MessagingServiceSID",
			Destination: &twilioMessagingServiceSID,
		},
		cli.StringFlag{
			Name:        "smtp-server",
			Usage:       "Host of smtp server",
			Destination: &smtpserver,
		},
		cli.StringFlag{
			Name:        "smtp-user",
			Usage:       "User to login smtp server",
			Destination: &smtpuser,
		},
		cli.StringFlag{
			Name:        "smtp-password",
			Usage:       "Password of smtp server",
			Destination: &smtppassword,
		},
		cli.IntFlag{
			Name:        "smtp-port",
			Usage:       "Port of smtp server",
			Destination: &smtpport,
			Value:       587,
		},
		cli.StringFlag{
			Name:        "SmsAeroUser",
			Usage:       "User for SmsAero",
			Destination: &smsAeroUser,
		},
		cli.StringFlag{
			Name:        "SmsAeroPassword",
			Usage:       "Password in md5 format or an apikey for SmsAero",
			Destination: &smsAeroPassword,
		},
		cli.StringFlag{
			Name:        "SmsAeroSenderId",
			Usage:       "The sender Id for SmsAero, filled in in the from field",
			Destination: &smsAeroSenderId,
		},
		cli.BoolFlag{
			Name:        "testEnv",
			Usage:       "Designate if this is a production environment",
			Destination: &testEnv,
		},
		cli.StringFlag{
			Name:        "iam-host",
			Usage:       "Host name of the iam service, e.g. iam.gig.tech",
			Destination: &iamHostName,
		},
		cli.StringFlag{
			Name:        "root-org",
			Usage:       "Name of the Root Organization",
			Destination: &rootOrg,
		},
		cli.StringFlag{
			Name:        "owner-email",
			Usage:       "Email of the Root User",
			Destination: &ownerEmail,
		},
		cli.StringFlag{
			Name:        "support-email",
			Usage:       "Email to be used for support, login/registration/forget-password",
			Destination: &supportEmail,
			Value:       "<EMAIL>",
		},
		cli.StringFlag{
			Name:        "api-key",
			Usage:       "Root organization API key",
			Destination: &apiKey,
		},
		cli.StringFlag{
			Name:        "callback-url",
			Usage:       "Callback URL, e.g. https://meneja.gig.tech/callback",
			Destination: &callbackURL,
		},
	}
	// Add CLI commands for admin operations
	app.Commands = []cli.Command{
		{
			Name:  "admin",
			Usage: "Administrative commands",
			Subcommands: []cli.Command{
				{
					Name:  "oidc-providers",
					Usage: "Manage OIDC providers",
					Subcommands: []cli.Command{
						{
							Name:      "list",
							Usage:     "List OIDC providers",
							ArgsUsage: "[--active]",
							Flags: []cli.Flag{
								cli.BoolFlag{
									Name:  "active",
									Usage: "Only show active providers",
								},
								cli.BoolFlag{
									Name:  "json",
									Usage: "Output in JSON format",
								},
							},
							Action: func(c *cli.Context) {
								oauthservice.HandleOIDCProvidersList(c, dbConnectionString)
							},
						},
						{
							Name:      "get",
							Usage:     "Get OIDC provider by ID",
							ArgsUsage: "<provider-id>",
							Flags: []cli.Flag{
								cli.BoolFlag{
									Name:  "json",
									Usage: "Output in JSON format",
								},
							},
							Action: func(c *cli.Context) {
								oauthservice.HandleOIDCProviderGet(c, dbConnectionString)
							},
						},
						{
							Name:      "create",
							Usage:     "Create OIDC provider from JSON file",
							ArgsUsage: "<config-file>",
							Flags: []cli.Flag{
								cli.BoolFlag{
									Name:  "json",
									Usage: "Output in JSON format",
								},
							},
							Action: func(c *cli.Context) {
								oauthservice.HandleOIDCProviderCreate(c, dbConnectionString)
							},
						},
						{
							Name:      "update",
							Usage:     "Update OIDC provider",
							ArgsUsage: "<provider-id> <config-file>",
							Flags: []cli.Flag{
								cli.BoolFlag{
									Name:  "json",
									Usage: "Output in JSON format",
								},
							},
							Action: func(c *cli.Context) {
								oauthservice.HandleOIDCProviderUpdate(c, dbConnectionString)
							},
						},
						{
							Name:      "delete",
							Usage:     "Delete OIDC provider",
							ArgsUsage: "<provider-id>",
							Flags: []cli.Flag{
								cli.BoolFlag{
									Name:  "json",
									Usage: "Output in JSON format",
								},
							},
							Action: func(c *cli.Context) {
								oauthservice.HandleOIDCProviderDelete(c, dbConnectionString)
							},
						},
						{
							Name:      "activate",
							Usage:     "Activate OIDC provider",
							ArgsUsage: "<provider-id>",
							Flags: []cli.Flag{
								cli.BoolFlag{
									Name:  "json",
									Usage: "Output in JSON format",
								},
							},
							Action: func(c *cli.Context) {
								oauthservice.HandleOIDCProviderActivate(c, dbConnectionString)
							},
						},
						{
							Name:      "deactivate",
							Usage:     "Deactivate OIDC provider",
							ArgsUsage: "<provider-id>",
							Flags: []cli.Flag{
								cli.BoolFlag{
									Name:  "json",
									Usage: "Output in JSON format",
								},
							},
							Action: func(c *cli.Context) {
								oauthservice.HandleOIDCProviderDeactivate(c, dbConnectionString)
							},
						},
					},
				},
				{
					Name:  "password-login",
					Usage: "Manage password login settings",
					Subcommands: []cli.Command{
						{
							Name:  "enable",
							Usage: "Enable password login",
							Flags: []cli.Flag{
								cli.BoolFlag{
									Name:  "json",
									Usage: "Output in JSON format",
								},
							},
							Action: func(c *cli.Context) {
								oauthservice.HandlePasswordLoginEnable(c, dbConnectionString)
							},
						},
						{
							Name:  "disable",
							Usage: "Disable password login",
							Flags: []cli.Flag{
								cli.BoolFlag{
									Name:  "json",
									Usage: "Output in JSON format",
								},
							},
							Action: func(c *cli.Context) {
								oauthservice.HandlePasswordLoginDisable(c, dbConnectionString)
							},
						},
						{
							Name:  "status",
							Usage: "Get password login status",
							Flags: []cli.Flag{
								cli.BoolFlag{
									Name:  "json",
									Usage: "Output in JSON format",
								},
							},
							Action: func(c *cli.Context) {
								oauthservice.HandlePasswordLoginStatus(c, dbConnectionString)
							},
						},
					},
				},
			},
		},
	}

	app.Before = func(c *cli.Context) error {
		if debugLogging {
			log.SetLevel(log.DebugLevel)
			log.Debug("Debug logging enabled")
		}
		config.SetSupportEmail(supportEmail)
		return nil
	}

	app.Action = func(c *cli.Context) {

		log.Infoln(app.Name, "version", app.Version)
		// Connect to DB!
		go db.Connect(dbConnectionString)
		defer db.Close()
		cookieSecret := identityservice.GetCookieSecret()
		var smsService communication.SMSService
		var emailService communication.EmailService
		if twilioAccountSID != "" && smsAeroPassword != "" {
			smsService = &communication.SMSServiceProxySeparateRussia{
				DefaultSMSService: &communication.TwilioSMSService{
					AccountSID:          twilioAccountSID,
					AuthToken:           twilioAuthToken,
					MessagingServiceSID: twilioMessagingServiceSID,
				},
				RussianSMSService: &communication.SmsAeroSMSService{
					Username: smsAeroUser,
					Password: smsAeroPassword,
					SenderId: smsAeroSenderId,
				},
			}
		} else if twilioAccountSID != "" {
			smsService = &communication.TwilioSMSService{
				AccountSID:          twilioAccountSID,
				AuthToken:           twilioAuthToken,
				MessagingServiceSID: twilioMessagingServiceSID,
			}
		} else if smsAeroUser != "" {
			smsService = &communication.SmsAeroSMSService{
				Username: smsAeroUser,
				Password: smsAeroPassword,
			}
		} else {
			log.Warn("============================================================================")
			log.Warn("No valid Twilio Account provided, falling back to development implementation")
			log.Warn("============================================================================")
			smsService = &communication.DevSMSService{}
		}

		if smtpserver == "" {
			log.Warn("============================================================================")
			log.Warn("No valid SMTP server provided, falling back to development implementation")
			log.Warn("============================================================================")
			emailService = &communication.DevEmailService{}

		} else {
			emailService = communication.NewSMTPEmailService(smtpserver, smtpport, smtpuser, smtppassword, supportEmail)
		}

		// Max 5 sms per 10 mins per phone number
		smsService = communication.NewRateLimitedSMSService(600, 5, smsService)

		identityService := identityservice.NewService(smsService, emailService)
		siteService := siteservice.NewService(cookieSecret, smsService, emailService, identityService, version, testEnv)

		var jwtKey []byte
		var err error
		jwtKey = []byte(os.Getenv("JWT_KEY"))
		if len(jwtKey) == 0 {
			if _, e := os.Stat("devcert/jwt_key.pem"); e == nil {
				log.Warning("===============================================================================")
				log.Warning("This instance uses a development JWT signing key, don't do this in production !")
				log.Warning("===============================================================================")

				jwtKey, err = ioutil.ReadFile("devcert/jwt_key.pem")
			}
		}
		if err != nil {
			log.Fatal("Unable to load a valid key for signing JWT's: ", err)
		}
		ecdsaKey, err := jwt.ParseECPrivateKeyFromPEM(jwtKey)
		if err != nil {
			log.Fatal("Unable to load a valid key for signing JWT's: ", err)
		}
		security.JWTPublicKey = &ecdsaKey.PublicKey
		oauthService, err := oauthservice.NewService(siteService, identityService, ecdsaKey, iamHostName, cookieSecret)
		if err != nil {
			log.Fatal("Unable to create the oauthservice: ", err)
		}

		r := routes.GetRouter(siteService, identityService, oauthService)

		server := &http.Server{
			Addr:    bindAddress,
			Handler: r,
		}

		if testEnv {
			log.Warn("Running in test environment - forget account endpoints enabled")
		}
		config := globalconfig.NewManager()
		if rootOrg != "" {
			log.Info("Creating Root Organization ", rootOrg)
			hasOwners := false
			if err := dborganization.RootOrganizationCreation(rootOrg); err != nil {
				if err == db.ErrDuplicate {
					log.Debug("Root organization already exists")
					if ownerEmail != "" {
						org, _ := dborganization.GetOrganizationCli(rootOrg)
						// check if the Root Org has any owners
						hasOwners = (len(org.Owners) > 0)
					}
				} else {
					log.Fatal("Unable to create the Root Organization: ", err)
				}
			}
			if err == nil && ownerEmail != "" && !hasOwners {
				if err = inviteOwner(rootOrg, ownerEmail, iamHostName, identityService); err != nil {
					log.Fatal("Unable to invite the Root Organization Owner: ", err)
				}
			}
			if apiKey != "" {
				log.Info("Adding API key to the Root Organization")
				organization.CreateRootOrganizationAPIkey(rootOrg, apiKey, callbackURL)
			}
			globalconfig.InitModels()
			exists, err := config.Exists("rootOrg")
			if err != nil {
				log.Fatal(err)
			}
			var existsSame bool
			if exists {
				prevRootOrg, err := config.GetByKey("rootOrg")
				if err != nil {
					log.Fatal(err)
				}
				existsSame = prevRootOrg.Value == rootOrg
				if !existsSame {
					err = config.Delete("rootOrg")
					if err != nil {
						log.Fatal(err)
					}
				}
			}
			if !(exists && existsSame) {
				err = config.Insert(&globalconfig.GlobalConfig{Key: "rootOrg", Value: rootOrg})
				if err != nil {
					log.Fatal(err)
				}
			}
		}
		exists, err := config.Exists("iamHostName")
		if err != nil {
			log.Fatal(err)
		}
		if !exists {
			err = config.Insert(&globalconfig.GlobalConfig{Key: "iamHostName", Value: iamHostName})
			if err != nil {
				log.Fatal(err)
			}
		}
		// Go make magic over HTTP
		log.Info("Listening (http) on ", bindAddress)
		log.Fatal(server.ListenAndServe())
	}

	app.Run(os.Args)
}

// inviteOwner invites Root Org Owner by email
func inviteOwner(orgName string, ownerEmail string, hostName string, is *identityservice.Service) error {
	log.Info("Inviting Owner to the Root Organization ", ownerEmail)
	orgReq := &invitations.JoinOrganizationInvitation{
		Role:           invitations.RoleOwner,
		Organization:   orgName,
		User:           "username",
		Status:         invitations.RequestPending,
		Created:        db.DateTime(time.Now()),
		Method:         invitations.MethodEmail,
		EmailAddress:   ownerEmail,
		PhoneNumber:    "",
		Code:           "",
		IsOrganization: false,
	}
	// Create JoinRequest
	if err := invitations.InviteRootOwnerCli(orgReq); err != nil {
		log.Error("Error inviting owner: ", err.Error())
		return err
	}
	// Send invitation email
	if err := is.SendOrganizationInviteEmailCli(hostName, orgReq); err != nil {
		return err
	}
	return nil
}

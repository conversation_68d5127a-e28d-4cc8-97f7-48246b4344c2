package oauthservice

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"time"

	"github.com/urfave/cli"

	"git.gig.tech/gig-meneja/iam/db"
)

// CLIResponse represents a standardized CLI response
type CLIResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// outputResult outputs the result in the requested format
func outputResult(c *cli.Context, success bool, data interface{}, errorMsg string) {
	if c.<PERSON>ol("json") {
		response := CLIResponse{
			Success: success,
			Data:    data,
			Error:   errorMsg,
		}
		jsonBytes, _ := json.MarshalIndent(response, "", "  ")
		fmt.Println(string(jsonBytes))
	} else {
		if success {
			if data != nil {
				switch v := data.(type) {
				case string:
					fmt.Println(v)
				default:
					jsonBytes, _ := json.MarshalIndent(data, "", "  ")
					fmt.Println(string(jsonBytes))
				}
			} else {
				fmt.Println("Success")
			}
		} else {
			fmt.Fprintf(os.<PERSON>derr, "Error: %s\n", errorMsg)
			os.Exit(1)
		}
	}
}

// connectDB connects to the database for CLI operations
func connectDB(connectionString string) error {
	db.Connect(connectionString)
	// Wait a moment for connection to establish
	time.Sleep(100 * time.Millisecond)
	return nil
}

func HandleOIDCProvidersList(c *cli.Context, dbConnectionString string) {
	if err := connectDB(dbConnectionString); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to connect to database: %v", err))
		return
	}
	defer db.Close()

	// Create a mock request for the manager
	req, _ := http.NewRequest("GET", "/", nil)
	mgr := NewManager(req)

	onlyActive := c.Bool("active")
	providers, err := mgr.ListOIDCProviders(onlyActive)
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to list OIDC providers: %v", err))
		return
	}

	response := map[string]interface{}{
		"results": providers,
	}
	outputResult(c, true, response, "")
}

func HandleOIDCProviderGet(c *cli.Context, dbConnectionString string) {
	if len(c.Args()) < 1 {
		outputResult(c, false, nil, "Provider ID is required")
		return
	}

	if err := connectDB(dbConnectionString); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to connect to database: %v", err))
		return
	}
	defer db.Close()

	providerID := c.Args().First()
	req, _ := http.NewRequest("GET", "/", nil)
	mgr := NewManager(req)

	provider, err := mgr.GetOIDCProvider(providerID)
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to get OIDC provider: %v", err))
		return
	}
	if provider == nil {
		outputResult(c, false, nil, "Provider not found")
		return
	}

	outputResult(c, true, provider, "")
}

func HandleOIDCProviderCreate(c *cli.Context, dbConnectionString string) {
	if len(c.Args()) < 1 {
		outputResult(c, false, nil, "Config file path is required")
		return
	}

	configFile := c.Args().First()
	configData, err := ioutil.ReadFile(configFile)
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to read config file: %v", err))
		return
	}

	var config OIDCProviderConfig
	if err := json.Unmarshal(configData, &config); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to parse config file: %v", err))
		return
	}

	if err := connectDB(dbConnectionString); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to connect to database: %v", err))
		return
	}
	defer db.Close()

	// Validation
	if err := validateOIDCConfig(&config); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Invalid config: %v", err))
		return
	}

	if err := validateOIDCEndpoints(&config); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Invalid endpoints: %v", err))
		return
	}

	// Try to discover the OIDC configuration
	_, err = DiscoverOIDCConfiguration(config.Issuer)
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to discover OIDC configuration: %v", err))
		return
	}

	req, _ := http.NewRequest("POST", "/", nil)
	mgr := NewManager(req)

	// Check if provider with same name exists
	existing, err := mgr.ListOIDCProviders(false)
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to check existing providers: %v", err))
		return
	}

	for _, p := range existing {
		if p.Name == config.Name {
			outputResult(c, false, nil, "Provider with this name already exists")
			return
		}
	}

	// Set timestamps
	now := time.Now().Unix()
	config.CreatedAt = now
	config.UpdatedAt = now
	config.Active = true

	if err := mgr.CreateOIDCProvider(&config); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to create provider: %v", err))
		return
	}

	outputResult(c, true, config, "")
}

func HandleOIDCProviderUpdate(c *cli.Context, dbConnectionString string) {
	if len(c.Args()) < 2 {
		outputResult(c, false, nil, "Provider ID and config file path are required")
		return
	}

	providerID := c.Args().Get(0)
	configFile := c.Args().Get(1)

	configData, err := ioutil.ReadFile(configFile)
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to read config file: %v", err))
		return
	}

	var config OIDCProviderConfig
	if err := json.Unmarshal(configData, &config); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to parse config file: %v", err))
		return
	}

	if err := connectDB(dbConnectionString); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to connect to database: %v", err))
		return
	}
	defer db.Close()

	if err := validateOIDCConfig(&config); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Invalid config: %v", err))
		return
	}

	if err := validateOIDCEndpoints(&config); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Invalid endpoints: %v", err))
		return
	}

	// Try to discover the OIDC configuration
	_, err = DiscoverOIDCConfiguration(config.Issuer)
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to discover OIDC configuration: %v", err))
		return
	}

	req, _ := http.NewRequest("PUT", "/", nil)
	mgr := NewManager(req)
	config.ID = providerID
	config.UpdatedAt = time.Now().Unix()

	if err := mgr.UpdateOIDCProvider(&config); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to update provider: %v", err))
		return
	}

	outputResult(c, true, config, "")
}

func HandleOIDCProviderDelete(c *cli.Context, dbConnectionString string) {
	if len(c.Args()) < 1 {
		outputResult(c, false, nil, "Provider ID is required")
		return
	}

	if err := connectDB(dbConnectionString); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to connect to database: %v", err))
		return
	}
	defer db.Close()

	providerID := c.Args().First()
	req, _ := http.NewRequest("DELETE", "/", nil)
	mgr := NewManager(req)

	// First check password login status
	passwordStatus, err := mgr.GetPasswordLoginStatus()
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to check password login status: %v", err))
		return
	}

	// If password login is disabled, check for other active providers
	if !passwordStatus.Enabled {
		activeProviders, err := mgr.ListOIDCProviders(true)
		if err != nil {
			outputResult(c, false, nil, fmt.Sprintf("Failed to list active providers: %v", err))
			return
		}

		// If this is the only active provider and password login is disabled, prevent deletion
		if len(activeProviders) == 1 && activeProviders[0].ID == providerID {
			outputResult(c, false, nil, "Cannot delete the last authentication method. Enable password login or another provider first.")
			return
		}
	}

	if err := mgr.DeleteOIDCProvider(providerID); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to delete provider: %v", err))
		return
	}

	outputResult(c, true, nil, "")
}

func HandleOIDCProviderActivate(c *cli.Context, dbConnectionString string) {
	if len(c.Args()) < 1 {
		outputResult(c, false, nil, "Provider ID is required")
		return
	}

	if err := connectDB(dbConnectionString); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to connect to database: %v", err))
		return
	}
	defer db.Close()

	providerID := c.Args().First()
	req, _ := http.NewRequest("POST", "/", nil)
	mgr := NewManager(req)

	// Check if provider exists
	provider, err := mgr.GetOIDCProvider(providerID)
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to get provider: %v", err))
		return
	}
	if provider == nil {
		outputResult(c, false, nil, "Provider not found")
		return
	}

	if err := mgr.ActivateOIDCProvider(providerID); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to activate provider: %v", err))
		return
	}

	outputResult(c, true, nil, "")
}

func HandleOIDCProviderDeactivate(c *cli.Context, dbConnectionString string) {
	if len(c.Args()) < 1 {
		outputResult(c, false, nil, "Provider ID is required")
		return
	}

	if err := connectDB(dbConnectionString); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to connect to database: %v", err))
		return
	}
	defer db.Close()

	providerID := c.Args().First()
	req, _ := http.NewRequest("POST", "/", nil)
	mgr := NewManager(req)

	// Check if provider exists
	provider, err := mgr.GetOIDCProvider(providerID)
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to get provider: %v", err))
		return
	}
	if provider == nil {
		outputResult(c, false, nil, "Provider not found")
		return
	}

	// First check password login status
	passwordStatus, err := mgr.GetPasswordLoginStatus()
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to check password login status: %v", err))
		return
	}

	// If password login is disabled, check for other active providers
	if !passwordStatus.Enabled {
		activeProviders, err := mgr.ListOIDCProviders(true)
		if err != nil {
			outputResult(c, false, nil, fmt.Sprintf("Failed to list active providers: %v", err))
			return
		}

		// If this is the only active provider and password login is disabled, prevent deactivation
		if len(activeProviders) == 1 && activeProviders[0].ID == providerID {
			outputResult(c, false, nil, "Cannot deactivate the last authentication method. Enable password login or another provider first.")
			return
		}
	}

	if err := mgr.DeactivateOIDCProvider(providerID); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to deactivate provider: %v", err))
		return
	}

	outputResult(c, true, nil, "")
}

func HandlePasswordLoginEnable(c *cli.Context, dbConnectionString string) {
	if err := connectDB(dbConnectionString); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to connect to database: %v", err))
		return
	}
	defer db.Close()

	req, _ := http.NewRequest("POST", "/", nil)
	mgr := NewManager(req)

	err := mgr.EnablePasswordLogin()
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to enable password login: %v", err))
		return
	}

	outputResult(c, true, nil, "")
}

func HandlePasswordLoginDisable(c *cli.Context, dbConnectionString string) {
	if err := connectDB(dbConnectionString); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to connect to database: %v", err))
		return
	}
	defer db.Close()

	req, _ := http.NewRequest("POST", "/", nil)
	mgr := NewManager(req)

	activeProviders, err := mgr.ListOIDCProviders(true)
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to list active providers: %v", err))
		return
	}

	// If there are no active providers, prevent disabling password login
	if len(activeProviders) == 0 {
		outputResult(c, false, nil, "Cannot disable password login. Enable another provider first.")
		return
	}

	err = mgr.DisablePasswordLogin()
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to disable password login: %v", err))
		return
	}

	outputResult(c, true, nil, "")
}

func HandlePasswordLoginStatus(c *cli.Context, dbConnectionString string) {
	if err := connectDB(dbConnectionString); err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to connect to database: %v", err))
		return
	}
	defer db.Close()

	req, _ := http.NewRequest("GET", "/", nil)
	mgr := NewManager(req)

	status, err := mgr.GetPasswordLoginStatus()
	if err != nil {
		outputResult(c, false, nil, fmt.Sprintf("Failed to get password login status: %v", err))
		return
	}

	outputResult(c, true, status, "")
}

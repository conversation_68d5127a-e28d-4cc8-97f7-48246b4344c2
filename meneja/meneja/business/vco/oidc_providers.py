# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

"""
Business logic for interacting with IAM OIDC providers
"""

import json
import subprocess
import tempfile
import os
from flask import current_app
from meneja.lib.enumeration import EnvironmentName
from meneja.model.vco import VCO


def _get_iam_container_name(vco_id):
    """
    Get the IAM container name for a VCO

    Args:
        vco_id (str): VCO ID

    Returns:
        str: Container name
    """
    vco = VCO.get_by_id(vco_id, only=["iam_domain"])
    # Assuming container naming convention based on domain
    return f"iam_{vco.iam_domain.replace('.', '_').replace('-', '_')}"


def _execute_iam_cli_command(vco_id, command_args):
    """
    Execute IAM CLI command in the appropriate container

    Args:
        vco_id (str): VCO ID
        command_args (list): CLI command arguments

    Returns:
        dict: Command result
    """
    container_name = _get_iam_container_name(vco_id)

    # Build the docker exec command
    docker_cmd = [
        "docker", "exec", container_name,
        "/usr/src/app/iam"  # Assuming this is the binary path
    ] + command_args + ["--json"]  # Always request JSON output

    try:
        result = subprocess.run(
            docker_cmd,
            capture_output=True,
            text=True,
            check=True,
            timeout=30  # 30 second timeout
        )

        # Parse JSON response
        response = json.loads(result.stdout)

        if not response.get("success", False):
            error_msg = response.get("error", "Unknown error")
            current_app.logger.error(f"IAM CLI command failed: {error_msg}")
            raise Exception(error_msg)

        return response.get("data")

    except subprocess.TimeoutExpired:
        current_app.logger.error("IAM CLI command timed out")
        raise Exception("Command timed out")
    except subprocess.CalledProcessError as e:
        current_app.logger.error(f"IAM CLI command failed: {e.stderr}")
        raise Exception(f"Command failed: {e.stderr}")
    except json.JSONDecodeError as e:
        current_app.logger.error(f"Failed to parse IAM CLI response: {e}")
        raise Exception("Invalid response format")


def get_iam_base_url(vco_id):
    """
    Get the base URL for the IAM service

    Args:
        vco_id (str): VCO ID

    Returns:
        str: Base URL for the IAM service
    """
    vco = VCO.get_by_id(vco_id, only=["iam_domain"])
    is_https = EnvironmentName.current() != EnvironmentName.TEST
    protocol = "https" if is_https else "http"
    return f"{protocol}://{vco.iam_domain}"


def list_oidc_providers(vco_id, only_active=False):
    """
    List OIDC providers from the IAM service

    Args:
        vco_id (str): VCO ID
        only_active (bool): Whether to only return active providers

    Returns:
        list: List of OIDC providers
    """
    command_args = ["admin", "oidc-providers", "list"]
    if only_active:
        command_args.append("--active")

    result = _execute_iam_cli_command(vco_id, command_args)
    return result.get("results", [])


def get_oidc_provider(vco_id, provider_id):
    """
    Get an OIDC provider by ID

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID

    Returns:
        dict: OIDC provider details or None if not found
    """
    command_args = ["admin", "oidc-providers", "get", provider_id]

    try:
        return _execute_iam_cli_command(vco_id, command_args)
    except Exception as e:
        if "not found" in str(e).lower():
            return None
        raise


def create_oidc_provider(vco_id, provider_data):
    """
    Create a new OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_data (dict): OIDC provider configuration

    Returns:
        dict: Created OIDC provider
    """
    # Write config to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(provider_data, f)
        config_file = f.name

    try:
        # Copy file to container
        container_name = _get_iam_container_name(vco_id)
        container_config_path = f"/tmp/oidc_config_{provider_data.get('name', 'temp')}.json"

        subprocess.run([
            "docker", "cp", config_file, f"{container_name}:{container_config_path}"
        ], check=True)

        command_args = ["admin", "oidc-providers", "create", container_config_path]
        result = _execute_iam_cli_command(vco_id, command_args)

        # Clean up
        subprocess.run([
            "docker", "exec", container_name, "rm", container_config_path
        ], check=False)  # Don't fail if cleanup fails

        return result

    finally:
        # Clean up local temp file
        try:
            os.unlink(config_file)
        except:
            pass


def update_oidc_provider(vco_id, provider_id, provider_data):
    """
    Update an existing OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID
        provider_data (dict): Updated OIDC provider configuration

    Returns:
        dict: Updated OIDC provider
    """
    # Ensure the ID is set in the provider data
    provider_data["id"] = provider_id

    # Write config to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(provider_data, f)
        config_file = f.name

    try:
        # Copy file to container
        container_name = _get_iam_container_name(vco_id)
        container_config_path = f"/tmp/oidc_config_{provider_id}.json"

        subprocess.run([
            "docker", "cp", config_file, f"{container_name}:{container_config_path}"
        ], check=True)

        command_args = ["admin", "oidc-providers", "update", provider_id, container_config_path]
        result = _execute_iam_cli_command(vco_id, command_args)

        # Clean up
        subprocess.run([
            "docker", "exec", container_name, "rm", container_config_path
        ], check=False)  # Don't fail if cleanup fails

        return result

    finally:
        # Clean up local temp file
        try:
            os.unlink(config_file)
        except:
            pass


def delete_oidc_provider(vco_id, provider_id):
    """
    Delete an OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID

    Returns:
        bool: True if successful
    """
    command_args = ["admin", "oidc-providers", "delete", provider_id]
    _execute_iam_cli_command(vco_id, command_args)
    return True


def activate_oidc_provider(vco_id, provider_id):
    """
    Activate an OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID

    Returns:
        bool: True if successful
    """
    command_args = ["admin", "oidc-providers", "activate", provider_id]
    _execute_iam_cli_command(vco_id, command_args)
    return True


def deactivate_oidc_provider(vco_id, provider_id):
    """
    Deactivate an OIDC provider

    Args:
        vco_id (str): VCO ID
        provider_id (str): OIDC provider ID

    Returns:
        bool: True if successful
    """
    command_args = ["admin", "oidc-providers", "deactivate", provider_id]
    _execute_iam_cli_command(vco_id, command_args)
    return True


def get_password_login_status(vco_id):
    """
    Get the current status of password login

    Args:
        vco_id (str): VCO ID

    Returns:
        dict: Password login status
    """
    command_args = ["admin", "password-login", "status"]
    return _execute_iam_cli_command(vco_id, command_args)


def enable_password_login(vco_id):
    """
    Enable password-based login

    Args:
        vco_id (str): VCO ID

    Returns:
        bool: True if successful
    """
    command_args = ["admin", "password-login", "enable"]
    _execute_iam_cli_command(vco_id, command_args)
    return True


def disable_password_login(vco_id):
    """
    Disable password-based login

    Args:
        vco_id (str): VCO ID

    Returns:
        bool: True if successful
    """
    command_args = ["admin", "password-login", "disable"]
    _execute_iam_cli_command(vco_id, command_args)
    return True

# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

""" This file is dynamically generated. Edits must be done in template.jinja file"""
import os

from flask import Blueprint, make_response, redirect, render_template, send_file, url_for
from flask_itsyouonline import authenticated

from meneja.api.vco import get_vco_id
from meneja.business.auth import is_authenticated, is_vco_admin, requires_vco_admin
from meneja.lib.utils import DocsRenderer
from meneja.model.vco import VCO

TEMPLATE_FOLDER = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "server", "templates"))
STATIC_FOLDER = os.path.join(TEMPLATE_FOLDER, "documentation", "admin")
docs_bp = Blueprint(
    "docs_bp", __name__, template_folder=TEMPLATE_FOLDER, static_folder=STATIC_FOLDER, static_url_path=""
)


@docs_bp.route("/en/")
def home_en():
    vco_id = get_vco_id()
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    docs_path = os.path.join(vco_id, docs_path, "en", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/")
def home_fr():
    vco_id = get_vco_id()
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    docs_path = os.path.join(vco_id, docs_path, "fr", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/")
def home_es():
    vco_id = get_vco_id()
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    docs_path = os.path.join(vco_id, docs_path, "es", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/")
def home_nl():
    vco_id = get_vco_id()
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    docs_path = os.path.join(vco_id, docs_path, "nl", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/")
def home():
    return redirect(url_for("docs_bp.home_en"))


@docs_bp.route("/search/search_index.json")
def search_index():
    vco_id = get_vco_id()
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    search_index_path = os.path.join(vco_id, docs_path, "search", "search_index.json")
    return make_response(
        render_template(search_index_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/<lang>/report_bad_translation.js")
def report_translation_script(lang):
    return send_file("templates/report_bad_translation.js", mimetype="application/javascript")


@docs_bp.route("/en/extra.css")
def en_css():
    vco = VCO.get_by_id(get_vco_id(), only=["branding"])
    branding = vco.branding
    primary_color = branding.color_scheme.primary
    secondary_color = branding.color_scheme.secondary
    extra_css = f"""
    :root {{
        --md-primary-fg-color: { primary_color };
        --md-accent-fg-color:  { secondary_color };
        --md-typeset-a-color:  { secondary_color };
    }}
    """
    return make_response(extra_css, {"X-Robots-Tag": "noindex", "Content-Type": "text/css; charset=utf-8"})


@docs_bp.route("/fr/extra.css")
def fr_css():
    vco = VCO.get_by_id(get_vco_id(), only=["branding"])
    branding = vco.branding
    primary_color = branding.color_scheme.primary
    secondary_color = branding.color_scheme.secondary
    extra_css = f"""
    :root {{
        --md-primary-fg-color: { primary_color };
        --md-accent-fg-color:  { secondary_color };
        --md-typeset-a-color:  { secondary_color };
    }}
    """
    return make_response(extra_css, {"X-Robots-Tag": "noindex", "Content-Type": "text/css; charset=utf-8"})


@docs_bp.route("/nl/extra.css")
def nl_css():
    vco = VCO.get_by_id(get_vco_id(), only=["branding"])
    branding = vco.branding
    primary_color = branding.color_scheme.primary
    secondary_color = branding.color_scheme.secondary
    extra_css = f"""
    :root {{
        --md-primary-fg-color: { primary_color };
        --md-accent-fg-color:  { secondary_color };
        --md-typeset-a-color:  { secondary_color };
    }}
    """
    return make_response(extra_css, {"X-Robots-Tag": "noindex", "Content-Type": "text/css; charset=utf-8"})


@docs_bp.route("/es/extra.css")
def es_css():
    vco = VCO.get_by_id(get_vco_id(), only=["branding"])
    branding = vco.branding
    primary_color = branding.color_scheme.primary
    secondary_color = branding.color_scheme.secondary
    extra_css = f"""
    :root {{
        --md-primary-fg-color: { primary_color };
        --md-accent-fg-color:  { secondary_color };
        --md-typeset-a-color:  { secondary_color };
    }}
    """
    return make_response(extra_css, {"X-Robots-Tag": "noindex", "Content-Type": "text/css; charset=utf-8"})


@docs_bp.route("/en/cloud_admin/")
@authenticated
@requires_vco_admin
def en_cloud_admin():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/cloud_admin", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/adminNotifications/")
def en_adminNotifications():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/adminNotifications", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/demoUser/")
def en_demoUser():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/demoUser", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/admin/")
def en_admin():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/admin", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/role_management/")
def en_role_management():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/role_management", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/concepts/")
def en_concepts():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/concepts", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/locations/")
def en_locations():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/locations", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/Cloudspace/")
def en_Cloudspace():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/Cloudspace", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/VirtualMachine/")
def en_VirtualMachine():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/VirtualMachine", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/Objectspace/")
def en_Objectspace():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/Objectspace", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/containerspaces/")
def en_containerspaces():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/containerspaces", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/disks/")
def en_disks():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/disks", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/images/")
def en_images():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/images", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/ingress/")
def en_ingress():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/ingress", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/dns/")
def en_dns():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/dns", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/recycleBin/")
def en_recycleBin():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/recycleBin", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/software_license/")
def en_software_license():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/software_license", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/manageVFW/")
def en_manageVFW():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/manageVFW", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/kubernetes/")
def en_kubernetes():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/kubernetes", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/k8s/")
def en_k8s():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/k8s", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/moveVMs/")
def en_moveVMs():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/moveVMs", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/resetpassword/")
def en_resetpassword():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/resetpassword", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/sophosXG/")
def en_sophosXG():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/sophosXG", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/cloneVM/")
def en_cloneVM():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/cloneVM", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/installVirtio/")
def en_installVirtio():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/installVirtio", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/qemuAgent/")
def en_qemuAgent():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/qemuAgent", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/cloud_init/")
def en_cloud_init():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/cloud_init", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/routeVM/")
def en_routeVM():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/routeVM", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/migration/")
def en_migration():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/migration", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/deployS3Storage/")
def en_deployS3Storage():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/deployS3Storage", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/deployS3CLI/")
def en_deployS3CLI():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/deployS3CLI", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/deployS3API/")
def en_deployS3API():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/deployS3API", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/checkmk/")
def en_checkmk():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/checkmk", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/wireguard/")
def en_wireguard():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/wireguard", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/cliGuide/")
def en_cliGuide():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/cliGuide", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/export_import_vms/")
def en_export_import_vms():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/export_import_vms", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/usingTerraform/")
def en_usingTerraform():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/usingTerraform", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/identity_management/")
def en_identity_management():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/identity_management", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/identity_management_extension/")
def en_identity_management_extension():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/identity_management_extension", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/spendings/")
def en_spendings():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/spendings", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/billing/")
def en_billing():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/billing", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/userNotifications/")
def en_userNotifications():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/userNotifications", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/en/downloads/")
def en_downloads():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "en/downloads", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/cloud_admin/")
@authenticated
@requires_vco_admin
def fr_cloud_admin():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/cloud_admin", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/adminNotifications/")
def fr_adminNotifications():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/adminNotifications", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/demoUser/")
def fr_demoUser():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/demoUser", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/admin/")
def fr_admin():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/admin", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/role_management/")
def fr_role_management():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/role_management", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/concepts/")
def fr_concepts():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/concepts", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/locations/")
def fr_locations():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/locations", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/Cloudspace/")
def fr_Cloudspace():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/Cloudspace", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/VirtualMachine/")
def fr_VirtualMachine():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/VirtualMachine", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/Objectspace/")
def fr_Objectspace():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/Objectspace", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/containerspaces/")
def fr_containerspaces():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/containerspaces", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/disks/")
def fr_disks():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/disks", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/images/")
def fr_images():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/images", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/ingress/")
def fr_ingress():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/ingress", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/dns/")
def fr_dns():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/dns", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/recycleBin/")
def fr_recycleBin():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/recycleBin", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/software_license/")
def fr_software_license():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/software_license", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/manageVFW/")
def fr_manageVFW():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/manageVFW", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/kubernetes/")
def fr_kubernetes():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/kubernetes", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/k8s/")
def fr_k8s():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/k8s", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/moveVMs/")
def fr_moveVMs():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/moveVMs", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/resetpassword/")
def fr_resetpassword():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/resetpassword", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/sophosXG/")
def fr_sophosXG():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/sophosXG", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/cloneVM/")
def fr_cloneVM():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/cloneVM", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/installVirtio/")
def fr_installVirtio():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/installVirtio", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/qemuAgent/")
def fr_qemuAgent():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/qemuAgent", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/cloud_init/")
def fr_cloud_init():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/cloud_init", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/routeVM/")
def fr_routeVM():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/routeVM", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/migration/")
def fr_migration():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/migration", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/deployS3Storage/")
def fr_deployS3Storage():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/deployS3Storage", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/deployS3CLI/")
def fr_deployS3CLI():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/deployS3CLI", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/deployS3API/")
def fr_deployS3API():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/deployS3API", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/checkmk/")
def fr_checkmk():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/checkmk", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/wireguard/")
def fr_wireguard():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/wireguard", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/cliGuide/")
def fr_cliGuide():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/cliGuide", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/export_import_vms/")
def fr_export_import_vms():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/export_import_vms", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/usingTerraform/")
def fr_usingTerraform():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/usingTerraform", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/identity_management/")
def fr_identity_management():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/identity_management", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/identity_management_extension/")
def fr_identity_management_extension():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/identity_management_extension", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/spendings/")
def fr_spendings():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/spendings", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/billing/")
def fr_billing():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/billing", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/userNotifications/")
def fr_userNotifications():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/userNotifications", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/fr/downloads/")
def fr_downloads():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "fr/downloads", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/cloud_admin/")
@authenticated
@requires_vco_admin
def es_cloud_admin():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/cloud_admin", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/adminNotifications/")
def es_adminNotifications():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/adminNotifications", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/demoUser/")
def es_demoUser():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/demoUser", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/admin/")
def es_admin():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/admin", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/role_management/")
def es_role_management():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/role_management", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/concepts/")
def es_concepts():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/concepts", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/locations/")
def es_locations():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/locations", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/Cloudspace/")
def es_Cloudspace():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/Cloudspace", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/VirtualMachine/")
def es_VirtualMachine():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/VirtualMachine", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/Objectspace/")
def es_Objectspace():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/Objectspace", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/containerspaces/")
def es_containerspaces():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/containerspaces", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/disks/")
def es_disks():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/disks", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/images/")
def es_images():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/images", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/ingress/")
def es_ingress():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/ingress", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/dns/")
def es_dns():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/dns", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/recycleBin/")
def es_recycleBin():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/recycleBin", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/software_license/")
def es_software_license():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/software_license", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/manageVFW/")
def es_manageVFW():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/manageVFW", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/kubernetes/")
def es_kubernetes():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/kubernetes", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/k8s/")
def es_k8s():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/k8s", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/moveVMs/")
def es_moveVMs():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/moveVMs", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/resetpassword/")
def es_resetpassword():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/resetpassword", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/sophosXG/")
def es_sophosXG():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/sophosXG", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/cloneVM/")
def es_cloneVM():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/cloneVM", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/installVirtio/")
def es_installVirtio():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/installVirtio", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/qemuAgent/")
def es_qemuAgent():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/qemuAgent", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/cloud_init/")
def es_cloud_init():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/cloud_init", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/routeVM/")
def es_routeVM():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/routeVM", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/migration/")
def es_migration():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/migration", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/deployS3Storage/")
def es_deployS3Storage():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/deployS3Storage", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/deployS3CLI/")
def es_deployS3CLI():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/deployS3CLI", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/deployS3API/")
def es_deployS3API():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/deployS3API", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/checkmk/")
def es_checkmk():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/checkmk", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/wireguard/")
def es_wireguard():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/wireguard", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/cliGuide/")
def es_cliGuide():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/cliGuide", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/export_import_vms/")
def es_export_import_vms():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/export_import_vms", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/usingTerraform/")
def es_usingTerraform():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/usingTerraform", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/identity_management/")
def es_identity_management():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/identity_management", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/identity_management_extension/")
def es_identity_management_extension():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/identity_management_extension", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/spendings/")
def es_spendings():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/spendings", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/billing/")
def es_billing():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/billing", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/userNotifications/")
def es_userNotifications():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/userNotifications", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/es/downloads/")
def es_downloads():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "es/downloads", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/cloud_admin/")
@authenticated
@requires_vco_admin
def nl_cloud_admin():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/cloud_admin", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/adminNotifications/")
def nl_adminNotifications():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/adminNotifications", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/demoUser/")
def nl_demoUser():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/demoUser", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/admin/")
def nl_admin():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/admin", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/role_management/")
def nl_role_management():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/role_management", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/concepts/")
def nl_concepts():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/concepts", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/locations/")
def nl_locations():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/locations", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/Cloudspace/")
def nl_Cloudspace():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/Cloudspace", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/VirtualMachine/")
def nl_VirtualMachine():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/VirtualMachine", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/Objectspace/")
def nl_Objectspace():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/Objectspace", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/containerspaces/")
def nl_containerspaces():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/containerspaces", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/disks/")
def nl_disks():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/disks", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/images/")
def nl_images():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/images", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/ingress/")
def nl_ingress():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/ingress", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/dns/")
def nl_dns():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/dns", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/recycleBin/")
def nl_recycleBin():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/recycleBin", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/software_license/")
def nl_software_license():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/software_license", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/manageVFW/")
def nl_manageVFW():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/manageVFW", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/kubernetes/")
def nl_kubernetes():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/kubernetes", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/k8s/")
def nl_k8s():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/k8s", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/moveVMs/")
def nl_moveVMs():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/moveVMs", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/resetpassword/")
def nl_resetpassword():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/resetpassword", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/sophosXG/")
def nl_sophosXG():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/sophosXG", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/cloneVM/")
def nl_cloneVM():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/cloneVM", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/installVirtio/")
def nl_installVirtio():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/installVirtio", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/qemuAgent/")
def nl_qemuAgent():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/qemuAgent", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/cloud_init/")
def nl_cloud_init():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/cloud_init", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/routeVM/")
def nl_routeVM():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/routeVM", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/migration/")
def nl_migration():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/migration", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/deployS3Storage/")
def nl_deployS3Storage():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/deployS3Storage", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/deployS3CLI/")
def nl_deployS3CLI():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/deployS3CLI", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/deployS3API/")
def nl_deployS3API():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/deployS3API", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/checkmk/")
def nl_checkmk():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/checkmk", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/wireguard/")
def nl_wireguard():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/wireguard", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/cliGuide/")
def nl_cliGuide():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/cliGuide", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/export_import_vms/")
def nl_export_import_vms():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/export_import_vms", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/usingTerraform/")
def nl_usingTerraform():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/usingTerraform", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/identity_management/")
def nl_identity_management():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/identity_management", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/identity_management_extension/")
def nl_identity_management_extension():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/identity_management_extension", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/spendings/")
def nl_spendings():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/spendings", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/billing/")
def nl_billing():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/billing", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/userNotifications/")
def nl_userNotifications():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/userNotifications", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.route("/nl/downloads/")
def nl_downloads():
    docs_path = "admin" if (is_authenticated() and is_vco_admin()) else "user"
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, docs_path, "nl/downloads", "index.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )


@docs_bp.errorhandler(404)
def handle_history_mode(error):
    vco_id = get_vco_id()
    docs_path = os.path.join(vco_id, "user", "404.html")
    return make_response(
        render_template(docs_path, **DocsRenderer.get_render_kwargs(vco_id)), {"X-Robots-Tag": "noindex"}
    )
